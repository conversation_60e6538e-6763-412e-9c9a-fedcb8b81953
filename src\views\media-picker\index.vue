<template>
  <div class="media-picker-container">
    <q-menu 
      dense 
      ref="menuRef" 
      v-model="menuShow" 
      anchor="top middle"
      self="top middle"
      @hide="hide"
    >
      <div class="media-picker">媒体选择器</div>
      <div class="media-picker-content">{{ id }} - {{ type }}</div>
      <q-btn dense flat label="确定" @click="onPick"></q-btn>
    </q-menu>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { QMenu } from "quasar";

const props = defineProps({
  id: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "",
  },
  resolve: {
    type: Function,
    default: () => {},
  },
  reject: {
    type: Function,
    default: () => {},
  },
});

const menuShow = ref(false);
const menuRef = ref<typeof QMenu>();

const show = () => {
  console.log("show: picker is open");
  menuShow.value = true;
};

const hide = () => {
  console.log("hide: picker is closed");
  menuShow.value = false;
};

const onPick = () => {
  console.log("onPick: picker is pick");
  menuShow.value = false;
  props.resolve({ id: "123", url: "https://123.com" });
};

defineExpose({
  show,
  hide,
});
</script>

<style scoped>
.media-picker-container {
  position: relative;
}

.media-picker {
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 300px;
}

.media-picker-content {
  padding: 10px;
  margin: 10px 0;
  background-color: #f5f5f5;
  border-radius: 4px;
}
</style>