### git 合并分支过程中遇到的merge问题处理

#### 一、本地修改文件，提交至本地仓库后，拉取远程仓库，发现有更新，会出现merge记录，解决方法如下：

1. 千万不要提交“同步更改”，因为这样会产生merge记录并且会推送到远程仓库，正确做法是将同步更改拆分为两个步骤，1 拉取；2 推送；
2. 拉取后，发现有更新，需要合并更新：首先先撤回本地的提交记录，规避掉merge记录，然后再合并更新
    2.1 如遇到无法撤销的情况，需要先退出merge，使用git merge --abort命令 或者 git merge --quit 退出merge
    2.2 然后撤回本地提交 git reset --soft HEAD~1 撤回提交
    2.3 暂存本地修改 git stash
    2.4 拉取远程最新修改 git pull
    2.5 恢复本地修改 git stash pop
    2.6 合并冲突后提交本地修改
    2.7 提交本地修改 git commit -m '提交信息'
    2.8 推送至远程仓库 git push
    2.9 如推送失败，需要先拉取一次，确保没有其他更新，然后再推送
3. 本地更改同一个文件的内容，需要先使用stash暂存，然后再合并更新，最后再pop出来
4. 合并更新后处理完，需要提交到本地仓库，然后推送至远程仓库（推送前再拉取一次，确保没有其他更新）
5. 如拉取还有更新，则步骤1-4重复执行。

#### 操作git的命令
1 git add . 提交至暂存区
2 git restore --staged . 撤回暂存区的提交
3 git commit -m '提交信息'
4 git reset --soft HEAD~1 撤回最近一次提交，属于软重置，保留本地修改
5 git reset --hard HEAD~1 撤回最近一次提交，属于硬重置，不保留本地修改
6 git reset --merge 取消合并更新
7 git stash 暂存本地修改
8 git stash pop 弹出暂存的修改
9 git pull 拉取远程仓库
10 git push 推送至远程仓库

#### 注意点
1 拉取下来发现有冲突，需要解决冲突（此过程中无法使用撤销提交的操作）；
2 需要完成或者退出合并状态之后才可以正常使用撤销提交操作；
3 撤销提交后可以先把自己本地的分支stash储藏一下；
4 拉取远程最新代码，然后合入本地stash修改；
5 合并解决完冲突之后，在进行提交和推送。

### 合并分支命令
1 git merge 合并分支
2 git merge --no-ff 合并分支，不使用快进合并
3 git merge --ff 合并分支，使用快进合并
4 git merge --ff-only 合并分支，使用快进合并，且只能快进合并
5 git merge --abort 取消合并分支
6 git merge --quit 退出合并分支
7 git merge --continue 继续合并分支
