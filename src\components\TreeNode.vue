<template>
  <draggable
    :list="node"
    :group="{ name: 'tree', pull: true }"
    :move="onCheckMove"
    class="tree-node"
    ghost-class="ghosting"
    drag-class="dragging"
    item-key="id"
    @start="onDragStart"
    @end="onDragEnd"
    @add="onDragAdd"
  >
    <template #item="{ element }">
      <div
        :class="[
          'tree-node-item',
          element.children && element.children.length > 0
            ? 'tree-node__parent'
            : 'tree-node__child',
        ]"
        :style="{ paddingLeft: `${level * 20}px` }"
      >
        <div
          clickable
          class="tree-item"
          @dragenter="onDragEnter"
          @dragover="onDragOver"
          @dragleave="onDragLeave"
          @drop="onDrop"
          @click="toggleExpanded(element)"
          @contextmenu.prevent="handleContextMenu($event, element)"
        >
          <div avatar v-if="element.children && element.children.length > 0">
            <q-icon
              :name="isExpanded(element) ? 'expand_more' : 'chevron_right'"
              size="sm"
              class="expand-icon"
            />
          </div>
          <div avatar v-else>
            <div class="expand-placeholder"></div>
          </div>
          <div>
            <q-item-label>{{ element.label }}</q-item-label>
          </div>
        </div>

        <!-- 子节点容器 -->
        <div
          class="children-container"
          :class="{ 'display-node': !isExpanded(element) }"
        >
          <TreeNode
            v-if="element.children && element.children.length > 0"
            :node="element.children || []"
            :level="level + 1"
            :expanded-set="expandedSet"
            @toggle-expanded="$emit('toggle-expanded', $event)"
            @drag-start="onDragStart"
            @drag-end="onDragEnd"
            @drag-add="onDragAdd"
            @drag-move="onCheckMove"
            @context-menu="(event, node) => $emit('context-menu', event, node)"
          />
        </div>
      </div>
    </template>
  </draggable>
</template>

<script setup lang="ts">
import draggable from "vuedraggable";
import type { TreeNode as TreeNodeType } from "../types/node";

const props = defineProps<{
  node: TreeNodeType[];
  level: number;
  expandedSet: Set<number>;
}>();

const emit = defineEmits<{
  "toggle-expanded": [nodeId: number];
  "drag-end": [e: any];
  "drag-start": [e: any];
  "drag-add": [e: any];
  "drag-move": [e: any];
  "context-menu": [event: MouseEvent, node: TreeNodeType];
}>();

const isExpanded = (node: TreeNodeType) => {
  return props.expandedSet.has(node.id);
};

function toggleExpanded(element: TreeNodeType) {
    console.log(element);
  if (element.children && element.children.length > 0) {
    emit("toggle-expanded", element.id);
  }
}

const onCheckMove = (e: any) => {
  emit('drag-move', e);
};

const onDragAdd = (e: any) => {
  emit('drag-add', e);
};

// 拖拽事件处理
const onDragStart = (e: any) => {
  emit('drag-start', e);
};

const onDragEnd = (e: any) => {
  emit('drag-end', e);
};

// 处理右键菜单
const handleContextMenu = (event: MouseEvent, node: TreeNodeType) => {
  event.preventDefault();
  emit('context-menu', event, node);
};

// 拖拽相关
const onDragEnter = (e: any) => {
  e.preventDefault();
  e.dataTransfer.dropEffect = 'move';
  e.target.classList.add('dragover');

};

const onDragOver = (e: any) => {
  e.preventDefault();
  e.dataTransfer.dropEffect = 'move';
  
};

const onDragLeave = (e: any) => {
  e.preventDefault();
  e.target.classList.remove('dragover');

};

const onDrop = (e: any) => {
  e.preventDefault();
  e.target.classList.remove('dragover');
};

</script>

<style scoped>
.tree-node {
  min-width: 200px;
  /* border: 1px solid #000; */
}

.dragging-active {
  /* background-color: #e5e5e5; */
}

.display-node {
  display: none !important;
}

.ghosting {
  overflow: hidden;
  height: 0px;
  border: 1px solid #000;
}

.dragging {
  opacity: 0;
}

.tree-node-item {
  position: relative;
}

.tree-item {
  display: flex;
  align-items: center;
  min-height: 30px;
  border-radius: 4px;
  margin: 2px 0;
  transition: background-color 0.2s ease;
}

.tree-item.dragover {
  background-color: #e5e5e5;
}

/* 正常状态下的 hover 效果 */
.tree-item:hover {
  background-color: #e5e5e5;
}

/* 拖拽时完全禁用 hover 效果 */
.tree-node.dragging-active .tree-item:hover {
  background-color: transparent !important;
}

/* 拖拽时禁用过渡动画，避免样式延迟 */
.tree-node.dragging-active .tree-item {
  transition: none !important;
}

.expand-icon {
  color: #666;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.expand-placeholder {
  width: 24px;
  height: 24px;
}

.children-container {
  position: relative;
  border-left: 1px solid #e0e0e0;
  margin-left: 12px;
}

.children-container::before {
  content: "";
  position: absolute;
  left: -1px;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: #e0e0e0;
}
</style>
