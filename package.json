{"name": "my-demo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@quasar/extras": "^1.17.0", "@zumer/snapdom": "^1.9.11", "dom-to-image-more": "^3.7.1", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "modern-screenshot": "^4.6.6", "pinia": "^3.0.3", "quasar": "^2.18.1", "vue": "^3.5.13", "vue-i18n": "^12.0.0-alpha.2", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@quasar/vite-plugin": "^1.9.0", "@types/node": "^24.0.3", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "sass": "^1.32.12", "typescript": "~5.8.3", "unocss": "^66.3.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}