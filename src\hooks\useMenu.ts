import { createApp, h } from "vue";
import MediaPicker from "../views/media-picker/index.vue";
/**
 * 通过useMenu可以动态挂载菜单
 * 使用方式：
 * const { selectMedia, destroyMenu } = useMenu();
 * const result = await selectMedia('123', 'image');
 */

interface MenuInstance {
  show: () => void;
  hide: () => void;
  id: string;
  type: string;
  resolve: (value: any) => void;
  reject: (reason?: any) => void;
}

let pickerInstance: MenuInstance | null = null;
let app: any = null;
let container: HTMLDivElement | null = null;

export const useMenu = () => {
  const selectMedia = (id: string, type: string) => {
    return new Promise((resolve, reject) => {
      if (!pickerInstance) {
        // 创建容器
        container = document.createElement("div");
        container.style.position = "fixed";
        container.style.top = "50%";
        container.style.left = "50%";
        container.style.transform = "translate(-50%, -50%)";
        container.style.zIndex = "9999";
        document.body.appendChild(container);

        // 创建根组件，使用函数式组件和 ref 回调
        const RootComponent = {
          setup() {
            let pickerRef: MenuInstance | null = null;
            
            const onRef = (el: any) => {
              if (el && typeof el.show === 'function') {
                pickerRef = el;
                pickerInstance = el;
                // 延迟调用 show，确保组件完全挂载
                setTimeout(() => {
                  el.show();
                }, 0);
              }
            };

            return {
              onRef,
              id,
              type,
              resolve,
              reject
            };
          },
          render() {
            return h(MediaPicker, {
              ref: this.onRef,
              id: this.id,
              type: this.type,
              resolve: this.resolve,
              reject: this.reject
            });
          }
        };

        app = createApp(RootComponent);
        app.mount(container);
      } else {
        // 更新现有实例的属性并显示
        pickerInstance.id = id;
        pickerInstance.type = type;
        pickerInstance.resolve = resolve;
        pickerInstance.reject = reject;
        pickerInstance.show();
      }
    });
  };

  const destroyMenu = () => {
    if (pickerInstance) {
      pickerInstance.hide();
    }
    if (app) {
      app.unmount();
    }
    if (container) {
      document.body.removeChild(container);
    }
    pickerInstance = null;
    app = null;
    container = null;
  };

  return {
    selectMedia,
    destroyMenu
  };
};