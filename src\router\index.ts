import { createRouter, createWebHistory } from "vue-router";

const routes = [
  // 默认路由重定向到首页
  { path: "/", redirect: "/home" },
  {
    path: "/home",
    component: () => import("@/views/home/<USER>"),
    meta: { title: "首页" }
  },
  {
    path: "/about",
    component: () => import("@/views/about/index.vue"),
    meta: { title: "关于" }
  },
  {
    path: "/tree",
    component: () => import("@/views/tree/index.vue"),
    meta: { title: "树组件" }
  },
  {
    path: "/treeds",
    component: () => import("@/views/treeds/index.vue"),
    meta: { title: "树形拖拽组件" }
  },
  {
    path: "/tree-cursor",
    component: () => import("@/views/tree-cursor/index.vue"),
    meta: { title: "Tree Cursor" }
  },
  {
    path: "/dom2Image",
    component: () => import("@/views/dom2Image/index.vue"),
    meta: { title: "dom2Image" }
  },
  // 404 页面处理
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: () => import("@/views/NotFound.vue")
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
