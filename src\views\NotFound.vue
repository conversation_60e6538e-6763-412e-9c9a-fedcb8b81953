<template>
  <div class="not-found-container">
    <div class="text-center">
      <q-icon name="error_outline" size="120px" color="grey-5" />
      <h1 class="text-h3 text-grey-7 q-mt-md">页面未找到</h1>
      <p class="text-body1 text-grey-6 q-mb-lg">
        抱歉，您访问的页面不存在。
      </p>
      <q-btn 
        color="primary" 
        label="返回首页" 
        icon="home"
        @click="$router.push('/home')"
        size="lg"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const $router = useRouter();
</script>

<style scoped>
.not-found-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 2rem;
}
</style>
