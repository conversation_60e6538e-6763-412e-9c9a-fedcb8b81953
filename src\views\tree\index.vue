<template>
  <div>
    <nested-draggable-tree :layers="layers" />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from "vue";
import NestedDraggableTree from "./NestedDraggableTree.vue";

export default defineComponent({
  components: {
    NestedDraggableTree,
  },
  setup() {
    const layers = ref([
      {
        id: 1,
        type: "container",
        name: "Container 1",
        visible: true,
        locked: false,
        children: [
          {
            id: 2,
            type: "component",
            name: "Component 1",
            visible: true,
            locked: false,
            children: [],
          },
          {
            id: 3,
            type: "shape",
            name: "Shape 1",
            visible: true,
            locked: false,
            children: [],
          },
        ],
      },
      // ... more layers
    ]);

    return {
      layers,
    };
  },
});
</script>

<style scoped>
/* Add your styles here */
</style>
