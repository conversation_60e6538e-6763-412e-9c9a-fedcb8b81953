<template>
  <div>
    <h1>Home</h1>
    <p>当前用户：{{ userStore.name }}</p>
    <p>当前用户年纪：{{ userStore.age }}</p>
    <p>当前计数：{{ count }}</p>
    <button @click="count++">增加计数</button>
    <q-separator />
    <q-btn flat dense @click="showMediaPicker"
      >按钮（打开媒体选择器-ref）</q-btn
    >
    <q-btn flat dense @click="showMediaPickerByHook"
      >按钮（打开媒体选择器-hook）</q-btn
    >
    <MediaPicker ref="mediaPickerRef" />
    <hr />
    <DoubleClickEdit :text="text" @update:text="onUpdateText" />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useUserStore } from "@/store/user";
import DoubleClickEdit from "@/components/DoubleClickEdit.vue";
import MediaPicker from "@/views/media-picker/index.vue";
import { useMenu } from "../../hooks/useMenu.ts";

const userStore = useUserStore();

const count = ref(0);

const text = ref("双击编辑");

const onUpdateText = (newText: string) => {
  text.value = newText;
};

const mediaPickerRef = ref<typeof MediaPicker>();

const showMediaPicker = () => {
  mediaPickerRef.value?.show();
};

// hooks
const { selectMedia } = useMenu();

const showMediaPickerByHook = async () => {
  try {
    const result = await selectMedia("123", "image");
    console.log(result);
  } catch (error) {
    console.log(error);
  }
};
</script>
