<template>
  <q-menu
    v-model="show"
    :position="position"
    context-menu
    class="context-menu bg-white shadow-lg rounded-md py-1 min-w-[120px]"
  >
    <q-list dense class="text-sm">
      <q-item clickable v-close-popup @click="$emit('rename')">
        <q-item-section>重命名</q-item-section>
        <q-item-section side>
          <q-icon name="edit" size="xs" />
        </q-item-section>
      </q-item>
      <q-item clickable v-close-popup @click="$emit('copy')">
        <q-item-section>复制</q-item-section>
        <q-item-section side>
          <q-icon name="content_copy" size="xs" />
        </q-item-section>
      </q-item>
      <q-item
        clickable
        v-close-popup
        :disable="!hasClipboard"
        @click="$emit('paste')"
      >
        <q-item-section :class="{ 'text-gray-400': !hasClipboard }"
          >粘贴</q-item-section
        >
        <q-item-section side>
          <q-icon
            name="content_paste"
            size="xs"
            :class="{ 'text-gray-400': !hasClipboard }"
          />
        </q-item-section>
      </q-item>
    </q-list>
  </q-menu>
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { Layer } from "./DragTree.vue";

const props = defineProps<{
  show: boolean;
  position: { x: number; y: number };
  selectedLayer: Layer | null;
}>();

const emit = defineEmits(["update:show", "rename", "copy", "paste"]);

const show = computed({
  get: () => props.show,
  set: (value) => emit("update:show", value),
});

// 从主组件获取剪贴板内容（实际实现中需要通过provide/inject或状态管理传递）
const hasClipboard = computed(() => {
  // 实际实现中应从主组件获取剪贴板状态
  return true; // 这里简化处理
});
</script>

<style scoped>
.context-menu {
  z-index: 2000;
}
</style>
