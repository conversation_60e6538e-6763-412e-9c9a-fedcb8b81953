### 拖拽优化

#### 1. 拖拽时的样式优化

- [x] 1.1 优化`.tree-item`的hover样式，使其在拖拽时不会显示hover样式
- [x] 1.2 优化`.tree-item`的选中样式，使其在拖拽时不会显示选中样式
- [x] 1.3 优化`.tree-item`的激活样式，使其在拖拽时不会显示激活样式


### 2. ghostClass 失效原因
拖拽开始时，vuedraggable会克隆拖拽元素作为ghost元素，此时会应用ghostClass样式；
但是ghost元素是原始元素的静态快照，不会响应vue的响应式更新，重新克隆添加is-dragging类名元素；

冲突机制：
1 当拖拽开始时，isDragging变为true；
2 原始元素会添加is-dragging类名；
3 但是ghost元素不会响应isDragging的改变，因此不会添加is-dragging类名；

导致 ghostClass 添加失效

解决方案：移除动态添加is-dragging类名的代码

### 3. 工作台告警问题
```txt
TreeNode.vue?t=1753514674867:163 [Vue warn]: Unhandled error during execution of beforeUnmount hook 
  at <Draggable list= (2) [{…}, {…}] group= {name: 'tree', pull: 'clone'} clone=fn<dragClone>  ... > 
  at <TreeNode key=0 node= (2) [{…}, {…}] level=1  ... > 
  at <Draggable list= (7) [{…}, {…}, {…}, {…}, {…}, {…}, {…}] group= {name: 'tree', pull: 'clone'} clone=fn<dragClone>  ... > 
  at <TreeNode node= (7) [{…}, {…}, {…}, {…}, {…}, {…}, {…}] level=0 expanded-set= Set(5) {1, 3, 4, 5, 9}  ... > 
  at <Tree> 
  at <Index onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< Proxy(Object) {__v_skip: true} > > 
  at <RouterView> 
  at <QPage class="q-pa-md" > 
  at <QPageContainer> 
  at <QLayout view="lHh lpR lFf" > 
  at <App>
```

暂时不关注

### 4. 克隆模式学习


### 5. 拖拽过程hover遗留问题
拖拽遗留问题解决方案：
拖拽开始时，添加dragging-active，同时将hover效果禁用（设置为transparent !important），禁用transition；
拖拽结束时，移除dragging-active；


### 6. 拖拽最近一级父元素容器添加背景色

### 7. 拖拽到元素（具有children属性，且不为undefined）的元素身上，添加背景色，拖拽离开时，移除背景色
### 8. 元素children属性为undefined的拖拽到身上无需添加背景色
