import { defineStore } from "pinia";

export const useUserStore = defineStore(
  "user",
  {
    state: () => ({
      name: "张三",
      age: 18,
    }),
    actions: {
      updateName(name: string) {
        this.name = name;
      },
    },
  },
//   {
//     persist: {
//       enabled: true,
//       strategies: [
//         {
//           storage: window.sessionStorage,
//         },
//       ],
//     },
//   }
);
