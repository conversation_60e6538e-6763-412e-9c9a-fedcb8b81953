import { defineComponent, ref, watch, shallowRef } from "vue";
import draggable from "vuedraggable";

export default defineComponent({
  name: "NestedDraggableTree",
  components: {
    draggable,
    NestedDraggableTree: shallowRef(() => import("./NestedDraggableTree.vue")),
  },
  props: {
    layers: {
      type: Array as () => any[],
      required: true,
    },
  },
  setup(props) {
    const searchText = ref("");
    const hoveredLayer = ref(null);
    const renamingLayer = ref(null);
    const selectedLayer = ref(null);
    const copiedLayer = ref(null);

    const filteredLayers = ref(props.layers);

    watch(searchText, (newSearchText) => {
      if (!newSearchText) {
        filteredLayers.value = props.layers;
        return;
      }
      filteredLayers.value = searchInTree(
        props.layers,
        newSearchText.toLowerCase()
      );
    });

    const onDragEnd = () => {
      console.log("Drag ended");
    };

    const showContextMenu = (layer: any) => {
      selectedLayer.value = layer;
    };

    const startRenaming = (layer: any) => {
      renamingLayer.value = layer;
    };

    const stopRenaming = () => {
      renamingLayer.value = null;
    };

    const copyLayer = (layer: any) => {
      copiedLayer.value = { ...layer };
    };

    const pasteLayer = () => {
      if (copiedLayer.value) {
        const newLayer = {
          ...copiedLayer.value,
          id: Date.now(),
          name: `${copiedLayer.value.type} ${props.layers.length + 1}`,
        };
        props.layers.push(newLayer);
        filteredLayers.value = props.layers;
      }
    };

    const toggleVisibility = (layer: any) => {
      layer.visible = !layer.visible;
    };

    const toggleLock = (layer: any) => {
      layer.locked = !layer.locked;
    };

    const getTypeIcon = (type: string) => {
      switch (type) {
        case "container":
          return "folder";
        case "component":
          return "widgets";
        case "shape":
          return "crop_square";
        case "text":
          return "text_fields";
        case "chart":
          return "bar_chart";
        default:
          return "";
      }
    };

    const getTypeName = (type: string) => {
      return type.charAt(0).toUpperCase() + type.slice(1);
    };

    const getTypeDescription = (type: string) => {
      switch (type) {
        case "container":
          return "容器";
        case "component":
          return "组件";
        case "shape":
          return "形状";
        case "text":
          return "文字";
        case "chart":
          return "图表";
        default:
          return "";
      }
    };

    const searchInTree = (nodes: any[], query: string): any[] => {
      let results: any[] = [];
      nodes.forEach((node) => {
        if (node.name.toLowerCase().includes(query)) {
          results.push(node);
        }
        if (node.children && node.children.length > 0) {
          const childrenResults = searchInTree(node.children, query);
          if (childrenResults.length > 0) {
            results.push({ ...node, children: childrenResults });
          }
        }
      });
      return results;
    };

    return {
      searchText,
      hoveredLayer,
      renamingLayer,
      selectedLayer,
      filteredLayers,
      onDragEnd,
      showContextMenu,
      startRenaming,
      stopRenaming,
      copyLayer,
      pasteLayer,
      toggleVisibility,
      toggleLock,
      getTypeIcon,
      getTypeName,
      getTypeDescription,
    };
  },
});
