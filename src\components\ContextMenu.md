# ContextMenu 右键菜单组件

一个功能完整的右键菜单组件，支持多级菜单、智能位置调整、键盘导航等特性。

## 特性

- 🎯 **智能定位**: 自动检测边界并调整菜单位置，防止超出视窗
- 📱 **响应式设计**: 支持移动端和桌面端，自适应屏幕尺寸
- 🔄 **连续右键**: 支持连续右键触发，菜单位置实时更新
- 📂 **多级菜单**: 支持二级子菜单，hover展开交互
- ⌨️ **键盘导航**: 支持 ESC 键关闭菜单
- 🎨 **自定义样式**: 支持图标、禁用状态、分隔线等
- 📏 **自动滚动**: 内容过多时自动显示滚动条
- 🔧 **TypeScript**: 完整的类型定义支持

## 安装

```bash
# 确保已安装 Quasar 和 Vue 3
npm install quasar @quasar/extras
```

## 基础用法

### 1. 导入组件

```vue
<script setup lang="ts">
import ContextMenu from '@/components/ContextMenu.vue'
import type { MenuItem, MenuPosition } from '@/types/node'
</script>
```

### 2. 基础示例

```vue
<template>
  <div @contextmenu="handleRightClick">
    右键点击这里
  </div>

  <ContextMenu
    :visible="menuVisible"
    :position="menuPosition"
    :items="menuItems"
    @close="closeMenu"
    @item-click="handleMenuClick"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'

const menuVisible = ref(false)
const menuPosition = ref({ x: 0, y: 0 })

const menuItems = ref([
  {
    name: 'copy',
    title: '复制',
    icon: 'content_copy',
    shortcuts: 'Ctrl+C'
  },
  {
    name: 'paste',
    title: '粘贴',
    icon: 'content_paste',
    shortcuts: 'Ctrl+V',
    disabled: true
  }
])

const handleRightClick = (event: MouseEvent) => {
  event.preventDefault()
  menuPosition.value = { x: event.clientX, y: event.clientY }
  menuVisible.value = true
}

const closeMenu = () => {
  menuVisible.value = false
}

const handleMenuClick = (item: MenuItem) => {
  console.log('点击菜单项:', item.name)
  closeMenu()
}
</script>
```

## API 文档

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `visible` | `boolean` | `false` | 菜单是否可见 |
| `position` | `MenuPosition` | `{ x: 0, y: 0 }` | 菜单显示位置 |
| `items` | `MenuItem[]` | `[]` | 菜单项列表 |
| `targetData` | `any` | `undefined` | 目标数据，会传递给事件回调 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `close` | - | 菜单关闭时触发 |
| `item-click` | `(item: MenuItem, targetData?: any)` | 菜单项点击时触发 |

### 类型定义

```typescript
interface MenuPosition {
  x: number
  y: number
}

interface MenuItem {
  name: string           // 唯一标识
  title: string          // 显示文本
  icon?: string          // 图标名称（Quasar 图标）
  shortcuts?: string     // 快捷键提示
  disabled?: boolean     // 是否禁用
  children?: MenuItem[]  // 子菜单项
}
```

## 高级用法

### 多级菜单

```vue
<script setup lang="ts">
const menuItems = ref([
  {
    name: 'edit',
    title: '编辑',
    icon: 'edit'
  },
  {
    name: 'more',
    title: '更多操作',
    icon: 'more_horiz',
    children: [
      {
        name: 'expand-all',
        title: '展开所有',
        icon: 'unfold_more'
      },
      {
        name: 'collapse-all',
        title: '收起所有',
        icon: 'unfold_less'
      },
      {
        name: 'export',
        title: '导出',
        icon: 'download'
      }
    ]
  },
  {
    name: 'delete',
    title: '删除',
    icon: 'delete',
    shortcuts: 'Delete'
  }
])
</script>
```

### 连续右键触发

组件自动支持连续右键触发，无需额外配置：

```vue
<script setup lang="ts">
const handleRightClick = (event: MouseEvent, data: any) => {
  event.preventDefault()

  // 更新位置和数据，菜单会自动调整位置
  menuPosition.value = { x: event.clientX, y: event.clientY }
  targetData.value = data
  menuVisible.value = true
}
</script>
```

### 动态菜单项

```vue
<script setup lang="ts">
import { computed } from 'vue'

const dynamicMenuItems = computed(() => {
  const baseItems = [
    { name: 'copy', title: '复制', icon: 'content_copy' }
  ]

  // 根据条件添加菜单项
  if (canPaste.value) {
    baseItems.push({
      name: 'paste',
      title: '粘贴',
      icon: 'content_paste'
    })
  }

  return baseItems
})
</script>
```

## 样式定制

### CSS 变量

```css
.context-menu {
  --menu-bg-color: white;
  --menu-border-color: #e0e0e0;
  --menu-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  --menu-border-radius: 6px;
  --item-hover-bg: #f5f5f5;
  --item-padding: 8px 12px;
  --item-font-size: 14px;
}
```

### 自定义样式

```vue
<style scoped>
/* 自定义菜单样式 */
:deep(.context-menu) {
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

/* 自定义菜单项样式 */
:deep(.context-menu-item) {
  padding: 10px 16px;
  font-size: 15px;
}

/* 自定义hover效果 */
:deep(.context-menu-item:hover) {
  background-color: #e3f2fd;
}
</style>
```

## 最佳实践

### 1. 菜单项设计

```typescript
// ✅ 推荐：清晰的菜单结构
const menuItems = [
  // 主要操作
  { name: 'edit', title: '编辑', icon: 'edit' },
  { name: 'copy', title: '复制', icon: 'content_copy', shortcuts: 'Ctrl+C' },

  // 分组操作
  {
    name: 'more',
    title: '更多操作',
    icon: 'more_horiz',
    children: [
      { name: 'rename', title: '重命名', icon: 'edit' },
      { name: 'duplicate', title: '复制', icon: 'file_copy' }
    ]
  },

  // 危险操作放在最后
  { name: 'delete', title: '删除', icon: 'delete', shortcuts: 'Delete' }
]

// ❌ 避免：过深的菜单嵌套（超过2级）
```

### 2. 事件处理

```typescript
// ✅ 推荐：统一的事件处理
const handleMenuClick = (item: MenuItem, targetData?: any) => {
  switch (item.name) {
    case 'copy':
      copyItem(targetData)
      break
    case 'delete':
      if (confirm('确定要删除吗？')) {
        deleteItem(targetData)
      }
      break
    default:
      console.warn('未处理的菜单项:', item.name)
  }
}

// ❌ 避免：在模板中直接处理复杂逻辑
```

### 3. 响应式设计

```vue
<script setup lang="ts">
import { computed } from 'vue'

// ✅ 推荐：根据屏幕尺寸调整菜单
const isMobile = computed(() => window.innerWidth < 768)

const menuItems = computed(() => {
  const items = baseMenuItems.value

  // 移动端简化菜单
  if (isMobile.value) {
    return items.filter(item => !item.advanced)
  }

  return items
})
</script>
```

### 4. 性能优化

```vue
<script setup lang="ts">
// ✅ 推荐：使用 shallowRef 优化大量菜单项
import { shallowRef } from 'vue'

const menuItems = shallowRef([
  // 大量菜单项...
])

// ✅ 推荐：避免在菜单显示时进行复杂计算
const preparedMenuItems = computed(() => {
  // 预处理菜单项
  return rawMenuItems.value.map(item => ({
    ...item,
    disabled: checkItemDisabled(item)
  }))
})
</script>
```

## 常见问题

### Q: 菜单位置不正确？

A: 确保传入正确的鼠标坐标：

```typescript
const handleRightClick = (event: MouseEvent) => {
  event.preventDefault()
  // 使用 clientX/Y 而不是 pageX/Y
  menuPosition.value = {
    x: event.clientX,
    y: event.clientY
  }
}
```

### Q: 子菜单不显示？

A: 检查菜单项是否有 `children` 属性：

```typescript
// ✅ 正确
{
  name: 'more',
  title: '更多',
  children: [
    { name: 'item1', title: '子项1' }
  ]
}

// ❌ 错误
{
  name: 'more',
  title: '更多'
  // 缺少 children 属性
}
```

### Q: 菜单在移动端显示异常？

A: 添加移动端适配样式：

```css
@media (max-width: 768px) {
  .context-menu {
    min-width: 200px;
    font-size: 16px;
  }

  .context-menu-item {
    padding: 12px 16px;
  }
}
```

## 更新日志

### v2.0.0

- 🎉 移除激活菜单逻辑，简化交互
- 🐛 修复子菜单hover展开问题
- ✨ 支持连续右键触发
- 🎨 优化样式和动画效果

### v1.0.0

- 🎉 初始版本发布
- ✨ 基础右键菜单功能
- 📱 响应式设计支持

一个通用的、可复用的右键菜单组件，支持多级菜单、键盘导航、响应式设计等功能。

## 完整示例

```vue
<template>
  <div @contextmenu.prevent="showContextMenu">
    右键点击这里显示菜单
  </div>
  
  <ContextMenu
    :visible="menuVisible"
    :position="menuPosition"
    :items="menuItems"
    @close="closeMenu"
    @item-click="handleMenuClick"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ContextMenu from '@/components/ContextMenu.vue';
import type { MenuItem, MenuPosition } from '@/types/node';

const menuVisible = ref(false);
const menuPosition = ref<MenuPosition>({ x: 0, y: 0 });

const menuItems = ref<MenuItem[]>([
  {
    name: 'copy',
    title: '复制',
    icon: 'content_copy',
    shortcuts: 'Ctrl+C'
  },
  {
    name: 'paste',
    title: '粘贴',
    icon: 'content_paste',
    shortcuts: 'Ctrl+V',
    disabled: true
  },
  {
    name: 'more',
    title: '更多选项',
    icon: 'more_horiz',
    children: [
      {
        name: 'option1',
        title: '选项1',
        icon: 'star'
      },
      {
        name: 'option2',
        title: '选项2',
        icon: 'favorite'
      }
    ]
  }
]);

const showContextMenu = (event: MouseEvent) => {
  menuPosition.value = { x: event.clientX, y: event.clientY };
  menuVisible.value = true;
};

const closeMenu = () => {
  menuVisible.value = false;
};

const handleMenuClick = (item: MenuItem) => {
  console.log('点击了菜单项:', item.name);
  // 处理菜单项点击逻辑
};
</script>
```

## 键盘支持

- **Esc键**: 关闭菜单

## 样式定制

组件使用 scoped 样式，可以通过 CSS 变量或深度选择器进行定制：

```css
/* 自定义菜单样式 */
:deep(.context-menu) {
  --menu-bg: #ffffff;
  --menu-border: #e0e0e0;
  --menu-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  --item-hover-bg: #f5f5f5;
  --item-active-bg: #e3f2fd;
}
```

## 注意事项

1. 菜单使用 `teleport` 渲染到 `body` 元素，确保正确的层级显示
2. 菜单会自动调整位置以防止超出视窗边界
3. 子菜单目前只支持一级嵌套
4. 建议在菜单项较多时使用分组或子菜单来组织内容
5. 禁用的菜单项不会触发点击事件

## 在树组件中的集成示例

参考 `Tree.vue` 组件中的实现，展示了如何在拖拽树组件中集成右键菜单功能。
