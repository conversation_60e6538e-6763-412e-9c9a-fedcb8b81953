<template>
  <div @dblclick="onDoubleClick">
    <span v-if="!isEdit">{{ text }}</span>
    <q-input
      v-else
      dense
      autofocus
      v-model="newText"
      @blur="onEditDone"
      @keyup.enter="onEditDone"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

const props = defineProps<{
  text: string;
}>();

const emit = defineEmits(["update:text"]);

const newText = ref(props.text);

const isEdit = ref(false);

const onEditDone = () => {
  emit("update:text", newText.value);
  isEdit.value = false;
};

const onDoubleClick = () => {
  isEdit.value = true;
};
</script>

<style scoped>
div {
  display: flex;
  align-items: center;
}
</style>
