<script setup lang="ts">
import NavigationMenu from "./components/NavigationMenu.vue";
</script>

<template>
  <q-layout view="lHh lpR lFf">
    <!-- 头部工具栏 -->
    <q-header elevated>
      <q-toolbar>
        <q-toolbar-title>
          Vue.js 导航演示
        </q-toolbar-title>
      </q-toolbar>
    </q-header>

    <!-- 左侧导航抽屉 -->
    <q-drawer
      :model-value="true"
      side="left"
      bordered
      class="bg-grey-1"
      :width="210"
      :breakpoint="0"
    >
      <NavigationMenu />
    </q-drawer>

    <!-- 主内容区域 -->
    <q-page-container>
      <q-page class="q-pa-md">
        <RouterView />
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<style scoped>
/* 可以在这里添加自定义样式 */
</style>
