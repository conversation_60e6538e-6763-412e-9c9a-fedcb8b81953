<template>
  <div class="navigation-menu">
    <!-- 导航头部 -->
    <div class="q-pa-md">
      <div class="text-h6 text-primary">导航菜单</div>
      <div class="text-caption text-grey-7">选择页面进行浏览</div>
    </div>

    <q-separator />

    <!-- 导航列表 -->
    <q-list>
      <q-item
        v-for="route in navigationRoutes"
        :key="route.path"
        :to="route.path"
        clickable
        v-ripple
        :active="currentPath === route.path"
        active-class="bg-primary text-white"
        class="q-my-xs q-mx-sm rounded-borders"
      >
        <q-item-section avatar>
          <q-icon :name="route.icon" />
        </q-item-section>

        <q-item-section>
          <q-item-label>{{ route.title }}</q-item-label>
          <q-item-label caption>
            {{ route.description }}
          </q-item-label>
        </q-item-section>

        <!-- 活动状态指示器 -->
        <!-- <q-item-section side v-if="currentPath === route.path">
          <q-icon name="chevron_right" />
        </q-item-section> -->
      </q-item>
    </q-list>

    <q-separator class="q-my-md" />

    <!-- 底部信息 -->
    <div class="q-pa-md">
      <div class="text-caption text-grey-6">当前路由: {{ currentPath }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
import { computed } from "vue";

// 获取当前路由信息
const route = useRoute();

// 创建响应式的当前路径
const currentPath = computed(() => route.path);

// 定义导航路由配置
const navigationRoutes = [
  {
    path: "/home",
    title: "首页",
    description: "主页面",
    icon: "home",
  },
  {
    path: "/about",
    title: "关于",
    description: "关于页面",
    icon: "info",
  },
  {
    path: "/tree",
    title: "树组件",
    description: "树形结构组件",
    icon: "account_tree",
  },
  {
    path: "/treeds",
    title: "树形拖拽组件",
    description: "可拖拽的树形组件",
    icon: "drag_indicator",
  },
  {
    path: "/tree-cursor",
    title: "Tree Cursor",
    description: "树形光标组件",
    icon: "account_tree",
  },
  {
    path: "/dom2Image",
    title: "dom2Image",
    description: "dom2Image组件",
    icon: "image",
  },
];
</script>

<style scoped>
.navigation-menu {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 自定义活动状态样式 */
.q-item.q-router-link--active {
  background-color: var(--q-primary);
  color: white;
}

.q-item.q-router-link--active .q-item__label--caption {
  color: rgba(255, 255, 255, 0.7);
}

/* 悬停效果 */
.q-item:not(.q-router-link--active):hover {
  background-color: rgba(0, 0, 0, 0.04);
}

/* 图标样式 */
.q-item__section--avatar .q-icon {
  font-size: 24px;
}

.q-item.q-item--active .q-item__label--caption {
  color: white;
}
</style>
