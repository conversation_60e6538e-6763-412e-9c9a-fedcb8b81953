export interface TreeNode {
  id: number;
  label: string;
  children?: TreeNode[];
  expanded?: boolean; // 控制节点是否展开
}

// 右键菜单相关类型定义
export interface MenuItem {
  name: string;      // 操作标识符/操作名称
  title: string;     // 显示的菜单文本
  icon?: string;     // 菜单项图标（可选）
  shortcuts?: string; // 快捷键显示文本（可选）
  disabled?: boolean; // 是否禁用（可选）
  children?: MenuItem[]; // 子菜单项（可选，支持多级菜单）
}

// 菜单项点击事件回调函数类型
export type MenuItemClickHandler = (menuItem: MenuItem, targetData?: any) => void;

// 右键菜单配置
export interface ContextMenuConfig {
  items: MenuItem[];
  onItemClick?: MenuItemClickHandler;
}

// 菜单位置信息
export interface MenuPosition {
  x: number;
  y: number;
}
