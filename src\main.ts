import { createApp } from "vue";
import { Quasar } from "quasar";

// Import icon libraries
import "@quasar/extras/material-icons/material-icons.css";

// Import Quasar css
import 'quasar/dist/quasar.css';

// main.ts
import 'virtual:uno.css';

import "./style.css";
import App from "./App.vue";
import { createPinia } from "pinia";
import i18n from "./locales/i18n";
import router from "./router";
import MediaPicker from "./views/media-picker/index.vue";

const pinia = createPinia();
const app = createApp(App);

app.use(Quasar, {
  plugins: {}, // import Quasar plugins and add here
});

app.component("MediaPicker", MediaPicker);

app.use(pinia);
app.use(i18n);
app.use(router);
app.mount("#app");
